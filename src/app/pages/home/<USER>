import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, On<PERSON>estroy, OnInit, inject } from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { Store } from '@ngrx/store';
import { ButtonModule } from 'primeng/button';
import { CardModule } from 'primeng/card';
import { CheckboxModule } from 'primeng/checkbox';
import { DatePickerModule } from 'primeng/datepicker';
import { IconFieldModule } from 'primeng/iconfield';
import { InputIconModule } from 'primeng/inputicon';
import { InputTextModule } from 'primeng/inputtext';
import { SelectModule } from 'primeng/select';
import { SelectButtonModule } from 'primeng/selectbutton';
import { TabsModule } from 'primeng/tabs';
import { TooltipModule } from 'primeng/tooltip';
import { Subscription } from 'rxjs';
import { debounceTime, distinctUntilChanged } from 'rxjs/operators';

import { SearchService } from 'app/services/search/search.service';
import * as SearchActions from 'app/store/search/search.actions';
import { SearchFormValues } from 'app/store/search/search.reducer';
import * as fromSearch from 'app/store/search/search.selectors';

// Interfaces for better type safety
interface SelectOption {
  label: string;
  value: string;
}

interface FilterField {
  label: string;
  placeholder: string;
  key: string;
}

interface DynamicFilterState {
  [key: string]: boolean;
}

/**
 * Custom validator to ensure start date is before end date
 */
function dateRangeValidator(control: any): any {
  const startDate = control.get('startDate')?.value;
  const endDate = control.get('endDate')?.value;

  if (!startDate || !endDate) {
    return null; // Don't validate if either date is missing
  }

  const start = new Date(startDate);
  const end = new Date(endDate);

  if (start >= end) {
    return { dateRangeInvalid: { message: 'Start date must be before end date' } };
  }

  return null;
}

@Component({
  selector: 'app-home',
  imports: [
    RouterModule,
    CommonModule,
    ReactiveFormsModule,
    TabsModule,
    CheckboxModule,
    InputTextModule,
    SelectModule,
    ButtonModule,
    CardModule,
    IconFieldModule,
    InputIconModule,
    SelectButtonModule,
    TooltipModule,
    DatePickerModule,
  ],
  templateUrl: './home.component.html',
  styleUrls: ['./home.component.scss'],
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class HomeComponent implements OnInit, OnDestroy {
  // Static configuration - readonly for better performance
  readonly searchTypeOptions: SelectOption[] = [
    { label: 'EDI Record', value: 'edi' },
    { label: 'App Record', value: 'app' },
  ];

  readonly directionOptions: SelectOption[] = [
    { label: 'Inbound & Outbound', value: 'inbound-outbound' },
    { label: 'Inbound', value: 'inbound' },
    { label: 'Outbound', value: 'outbound' },
  ];

  readonly transactionViewOptions: SelectOption[] = [
    { label: 'All Transaction Sets', value: 'all' },
    { label: 'Group View', value: 'group' },
  ];

  // Simplified static field definitions
  readonly staticInboundFields: FilterField[] = [
    { label: 'ISA Sender', placeholder: 'ISA Sender', key: 'inboundIsaSender' },
    { label: 'GS Sender', placeholder: 'GS Sender', key: 'inboundGsSender' },
  ];

  readonly staticOutboundFields: FilterField[] = [
    { label: 'ISA Receiver', placeholder: 'ISA Receiver', key: 'outboundIsaReceiver' },
    { label: 'GS Receiver', placeholder: 'GS Receiver', key: 'outboundGsReceiver' },
  ];

  // Dynamic filter options
  readonly additionalInboundOptions: SelectOption[] = [
    { label: 'ISA Receiver', value: 'isaReceiver' },
    { label: 'GS Receiver', value: 'gsReceiver' },
  ];

  readonly additionalOutboundOptions: SelectOption[] = [
    { label: 'ISA Sender', value: 'isaSender' },
    { label: 'GS Sender', value: 'gsSender' },
  ];

  readonly additionalEdiAttributesOptions: SelectOption[] = [
    { label: 'Message ID', value: 'messageId' },
    { label: 'Network', value: 'network' },
    { label: 'Message Status', value: 'messageStatus' },
    { label: 'Control Number', value: 'controlNumber' },
  ];

  // Form group for the search form
  searchForm!: FormGroup;

  // Local component state for show/hide of additional filters (not persisted)
  showAdditionalInboundFilters: DynamicFilterState = {
    isaReceiver: false,
    gsReceiver: false,
  };

  showAdditionalOutboundFilters: DynamicFilterState = {
    isaSender: false,
    gsSender: false,
  };

  showAdditionalEdiAttributesFilters: DynamicFilterState = {
    messageId: false,
    network: false,
    messageStatus: false,
    controlNumber: false,
  };

  // NgRx selectors and observables
  private readonly store = inject(Store);

  // Core state observables
  formValues$ = this.store.select(fromSearch.selectFormValues);
  hasSearched$ = this.store.select(fromSearch.selectHasSearched);
  isLoading$ = this.store.select(fromSearch.selectIsLoading);

  // Derived observables using selectors
  staticInboundFields$ = this.store.select(fromSearch.selectStaticInboundFields);
  staticOutboundFields$ = this.store.select(fromSearch.selectStaticOutboundFields);
  dynamicFieldValues$ = this.store.select(fromSearch.selectDynamicFieldValues);

  // Subscriptions
  private readonly subscriptions = new Subscription();

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private fb: FormBuilder,
    // Inject SearchService for potential future direct API calls
    private searchService: SearchService,
  ) {}

  ngOnInit() {
    this.initForm();

    // Subscribe to form values from the store with improved synchronization
    this.subscriptions.add(
      this.formValues$.subscribe(formValues => {
        if (formValues && this.searchForm) {
          // Update the form with values from the store
          this.updateFormFromState(formValues);
        }
      }),
    );

    // Subscribe to form changes and update store (debounced for performance)
    this.subscriptions.add(
      this.searchForm.valueChanges.pipe(
        debounceTime(300),
        distinctUntilChanged()
      ).subscribe(formValues => {
        // Only update store if form is valid and values have actually changed
        if (this.searchForm.valid && formValues) {
          this.store.dispatch(SearchActions.updateFormValues({
            formValues: formValues as Partial<SearchFormValues>
          }));
        }
      })
    );

    // Subscribe to query parameters to handle direct URL navigation
    this.subscriptions.add(
      this.route.queryParams.subscribe(params => {
        if (Object.keys(params).length > 0 && this.searchForm) {
          const formValues: any = {};

          Object.keys(params).forEach(key => {
            // Process inbound input values (support both old and new formats)
            if (key.startsWith('filter_dynamic_inbound_') || key.startsWith('filter_inbound_')) {
              const inputKey = key.startsWith('filter_dynamic_inbound_')
                ? key.replace('filter_dynamic_inbound_', '')
                : key.replace('filter_inbound_', '');
              const inboundKey = `inbound_${inputKey}`;
              formValues[inboundKey] = params[key];

              // Auto-show filter that has value from URL
              if (params[key] && params[key].trim()) {
                this.showAdditionalInboundFilters[inputKey as keyof typeof this.showAdditionalInboundFilters] = true;
              }
            }
            // Process outbound input values (support both old and new formats)
            else if (key.startsWith('filter_dynamic_outbound_') || key.startsWith('filter_outbound_')) {
              const inputKey = key.startsWith('filter_dynamic_outbound_')
                ? key.replace('filter_dynamic_outbound_', '')
                : key.replace('filter_outbound_', '');
              const outboundKey = `outbound_${inputKey}`;
              formValues[outboundKey] = params[key];

              // Auto-show filter that has value from URL
              if (params[key] && params[key].trim()) {
                this.showAdditionalOutboundFilters[inputKey as keyof typeof this.showAdditionalOutboundFilters] = true;
              }
            }
            // Process EDI attributes input values (support both old and new formats)
            else if (key.startsWith('filter_dynamic_edi_') || key.startsWith('filter_edi_')) {
              const inputKey = key.startsWith('filter_dynamic_edi_')
                ? key.replace('filter_dynamic_edi_', '')
                : key.replace('filter_edi_', '');
              formValues[inputKey] = params[key];

              // Auto-show filter that has value from URL
              if (params[key] && params[key].trim()) {
                this.showAdditionalEdiAttributesFilters[inputKey as keyof typeof this.showAdditionalEdiAttributesFilters] = true;
              }
            }
          });

          // Update the form with the extracted values if any exist
          if (Object.keys(formValues).length > 0) {
            this.updateFormFromState(formValues);
          }
        }
      }),
    );
  }

  ngOnDestroy() {
    // Clean up subscriptions
    this.subscriptions.unsubscribe();
  }

  /**
   * Initialize the reactive form with simplified structure
   */
  private initForm(): void {
    // Create the main form group with simplified structure
    this.searchForm = this.fb.nonNullable.group(
      {
        // Basic search options
        searchType: 'edi',
        direction: 'inbound-outbound',
        transactionView: 'all',
        equipmentSearchType: 'equipment',
        equipmentIds: '',
        transactionAttributes: '',

        // Date range
        startDate: new Date(new Date().setDate(new Date().getDate() - 15)),
        endDate: new Date(),

        // Inbound fields
        inbound_isaSender: '',
        inbound_gsSender: '',
        inbound_isaReceiver: '',
        inbound_gsReceiver: '',

        // Outbound fields
        outbound_isaReceiver: '',
        outbound_gsReceiver: '',
        outbound_isaSender: '',
        outbound_gsSender: '',

        // EDI attributes
        messageId: '',
        network: '',
        messageStatus: '',
        controlNumber: '',
      },
      { validators: [dateRangeValidator] },
    );

    // Subscribe to date changes to provide real-time validation feedback
    this.searchForm.get('startDate')?.valueChanges.subscribe(() => {
      this.searchForm.updateValueAndValidity();
    });

    this.searchForm.get('endDate')?.valueChanges.subscribe(() => {
      this.searchForm.updateValueAndValidity();
    });

    // Note: Show/hide state is now managed locally in component memory
  }

  /**
   * Clear input values when filters are hidden
   */
  private clearInputWhenHidden(filterType: 'inbound' | 'outbound' | 'edi', key: string): void {
    let formControlName: string;
    let isVisible: boolean;

    switch (filterType) {
      case 'inbound':
        formControlName = `inbound_${key}`;
        isVisible = this.showAdditionalInboundFilters[key as keyof typeof this.showAdditionalInboundFilters];
        break;
      case 'outbound':
        formControlName = `outbound_${key}`;
        isVisible = this.showAdditionalOutboundFilters[key as keyof typeof this.showAdditionalOutboundFilters];
        break;
      case 'edi':
        formControlName = key;
        isVisible = this.showAdditionalEdiAttributesFilters[key as keyof typeof this.showAdditionalEdiAttributesFilters];
        break;
    }

    if (!isVisible) {
      this.searchForm.get(formControlName)?.setValue('');
    }
  }

  // Optimized getters using NgRx selectors (for template usage)
  get staticInboundFieldsWithValues() {
    // For templates that need synchronous access, fall back to form values
    return this.staticInboundFields.map(field => ({
      ...field,
      value: this.searchForm?.get(field.key)?.value || '',
    }));
  }

  get staticOutboundFieldsWithValues() {
    // For templates that need synchronous access, fall back to form values
    return this.staticOutboundFields.map(field => ({
      ...field,
      value: this.searchForm?.get(field.key)?.value || '',
    }));
  }

  // Getter to check for date range validation errors
  get hasDateRangeError(): boolean {
    return this.searchForm?.hasError('dateRangeInvalid') || false;
  }

  // Getter to get the date range error message
  get dateRangeErrorMessage(): string {
    const error = this.searchForm?.getError('dateRangeInvalid');
    return error?.message || '';
  }

  // TrackBy functions for better performance
  trackByValue = (_: number, item: any) => item.value;

  // Helper methods to check show/hide states (using local component state)
  isInboundCheckboxChecked(key: string): boolean {
    return this.showAdditionalInboundFilters[key as keyof typeof this.showAdditionalInboundFilters] || false;
  }

  isOutboundCheckboxChecked(key: string): boolean {
    return this.showAdditionalOutboundFilters[key as keyof typeof this.showAdditionalOutboundFilters] || false;
  }

  isEdiCheckboxChecked(key: string): boolean {
    return this.showAdditionalEdiAttributesFilters[key as keyof typeof this.showAdditionalEdiAttributesFilters] || false;
  }

  // Helper methods to check if any checkboxes are checked (for conditional styling)
  hasAnyInboundChecked(): boolean {
    return Object.values(this.showAdditionalInboundFilters).some(value => value);
  }

  hasAnyOutboundChecked(): boolean {
    return Object.values(this.showAdditionalOutboundFilters).some(value => value);
  }

  hasAnyEdiAttributesChecked(): boolean {
    return Object.values(this.showAdditionalEdiAttributesFilters).some(value => value);
  }

  // Methods to toggle show/hide state and clear inputs when hidden
  toggleInboundFilter(key: string): void {
    const typedKey = key as keyof typeof this.showAdditionalInboundFilters;
    this.showAdditionalInboundFilters[typedKey] = !this.showAdditionalInboundFilters[typedKey];
    this.clearInputWhenHidden('inbound', key);
  }

  toggleOutboundFilter(key: string): void {
    const typedKey = key as keyof typeof this.showAdditionalOutboundFilters;
    this.showAdditionalOutboundFilters[typedKey] = !this.showAdditionalOutboundFilters[typedKey];
    this.clearInputWhenHidden('outbound', key);
  }

  toggleEdiAttributesFilter(key: string): void {
    const typedKey = key as keyof typeof this.showAdditionalEdiAttributesFilters;
    this.showAdditionalEdiAttributesFilters[typedKey] = !this.showAdditionalEdiAttributesFilters[typedKey];
    this.clearInputWhenHidden('edi', key);
  }

  /**
   * Submit the search form
   */
  onSearch(): void {
    if (!this.searchForm.valid) {
      // Mark all fields as touched to show validation errors
      this.searchForm.markAllAsTouched();

      // If there's a date range error, we could show a toast or alert here
      if (this.hasDateRangeError) {
        console.warn('Date range validation error:', this.dateRangeErrorMessage);
      }

      return;
    }

    // Set loading state
    this.store.dispatch(SearchActions.setLoading({ isLoading: true }));

    const formValues = this.searchForm.value as SearchFormValues;

    // Update form values in store using new action
    this.store.dispatch(SearchActions.updateFormValues({ formValues }));

    // Build query parameters
    const queryParams: Record<string, string> = {
      page: '1',
      pageSize: '20',
      sortBy: 'dateReceived',
      sortDirection: 'desc',
    };

    // Add search type filter
    if (formValues.searchType) {
      queryParams['filter_searchType'] = formValues.searchType;
    }

    // Add direction filter
    if (formValues.direction) {
      queryParams['filter_direction'] = formValues.direction;
    }

    // Add transaction view filter
    if (formValues.transactionView) {
      queryParams['filter_transactionView'] = formValues.transactionView;
    }

    // Add equipment IDs if present
    if (formValues.equipmentIds && formValues.equipmentIds.trim()) {
      queryParams['filter_equipmentIds'] = formValues.equipmentIds.trim();
      queryParams['filter_equipmentSearchType'] = formValues.equipmentSearchType;
    }

    // Add static inbound/outbound fields if present
    this.staticInboundFields.forEach(field => {
      const value = formValues[field.key];
      if (value && value.trim()) {
        queryParams[`filter_${field.key}`] = value.trim();
      }
    });

    this.staticOutboundFields.forEach(field => {
      const value = formValues[field.key];
      if (value && value.trim()) {
        queryParams[`filter_${field.key}`] = value.trim();
      }
    });

    // Add transaction attributes if present
    if (formValues.transactionAttributes && formValues.transactionAttributes.trim()) {
      queryParams['filter_transactionAttributes'] = formValues.transactionAttributes.trim();
    }

    // Add input values for checked additional filters
    this.additionalInboundOptions.forEach(option => {
      const inboundKey = `inbound_${option.value}`;
      const value = formValues[inboundKey];
      if (value && value.trim()) {
        queryParams[`filter_inbound_${option.value}`] = value.trim();
      }
    });

    this.additionalOutboundOptions.forEach(option => {
      const outboundKey = `outbound_${option.value}`;
      const value = formValues[outboundKey];
      if (value && value.trim()) {
        queryParams[`filter_outbound_${option.value}`] = value.trim();
      }
    });

    this.additionalEdiAttributesOptions.forEach(option => {
      const value = formValues[option.value];
      if (value && value.trim()) {
        queryParams[`filter_edi_${option.value}`] = value.trim();
      }
    });

    // Add start and end dates
    if (formValues.startDate) {
      const startMs = formValues.startDate.getTime();
      queryParams['filter_dateRangeStart'] = startMs.toString();
    }

    if (formValues.endDate) {
      const endMs = formValues.endDate.getTime();
      queryParams['filter_dateRangeEnd'] = endMs.toString();
    }

    // Save search parameters to the store using new action
    this.store.dispatch(SearchActions.saveSearchParams({ searchParams: queryParams }));

    // Navigate to search results with query parameters
    this.router.navigate(['/search-results'], { queryParams });

    // Also call API directly to pre-fetch results
    const searchParams = this.searchService.parseSearchParams(queryParams);
    this.searchService.search(searchParams).subscribe({
      next: (response) => {
        this.store.dispatch(SearchActions.setLoading({ isLoading: false }));
        console.log('Search response:', response);
      },
      error: (error) => {
        this.store.dispatch(SearchActions.setLoading({ isLoading: false }));
        console.error('Search error:', error);
      }
    });
  }

  /**
   * Reset the search form
   */
  onReset(): void {
    // Reset the store state first (this will trigger form updates)
    this.store.dispatch(SearchActions.resetState());

    // Reset the form to initial values using the form's reset method
    this.searchForm.reset({
      searchType: 'edi',
      direction: 'inbound-outbound',
      transactionView: 'all',
      equipmentSearchType: 'equipment',
      equipmentIds: '',
      transactionAttributes: '',
      startDate: new Date(new Date().setDate(new Date().getDate() - 15)),
      endDate: new Date(),

      // Reset inbound fields
      inbound_isaSender: '',
      inbound_gsSender: '',
      inbound_isaReceiver: '',
      inbound_gsReceiver: '',

      // Reset outbound fields
      outbound_isaReceiver: '',
      outbound_gsReceiver: '',
      outbound_isaSender: '',
      outbound_gsSender: '',

      // Reset EDI attributes
      messageId: '',
      network: '',
      messageStatus: '',
      controlNumber: '',
    });

    // Reset local show/hide state
    this.showAdditionalInboundFilters = {
      isaReceiver: false,
      gsReceiver: false,
    };

    this.showAdditionalOutboundFilters = {
      isaSender: false,
      gsSender: false,
    };

    this.showAdditionalEdiAttributesFilters = {
      messageId: false,
      network: false,
      messageStatus: false,
      controlNumber: false,
    };
  }

  /**
   * Update form values from the state with improved type safety
   */
  private updateFormFromState(formValues: SearchFormValues): void {
    if (!formValues || !this.searchForm) return;

    // Update form controls directly with the form values
    // This is more straightforward and avoids complex type issues
    this.searchForm.patchValue(formValues, { emitEvent: false });
  }
}
