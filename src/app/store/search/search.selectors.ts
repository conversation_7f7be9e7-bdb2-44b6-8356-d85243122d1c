import { createFeatureSelector, createSelector } from '@ngrx/store';

import { SearchStateModel } from './search.reducer';

// Feature selector
export const selectSearchState = createFeatureSelector<SearchStateModel>('search');

// Basic selectors
export const selectFormValues = createSelector(
  selectSearchState,
  (state: SearchStateModel) => state.formValues,
);

export const selectLastSearchParams = createSelector(
  selectSearchState,
  (state: SearchStateModel) => state.lastSearchParams,
);

export const selectHasSearched = createSelector(
  selectSearchState,
  (state: SearchStateModel) => state.hasSearched,
);

export const selectIsLoading = createSelector(
  selectSearchState,
  (state: SearchStateModel) => state.isLoading,
);

// Derived selectors for better component integration
export const selectStaticInboundFields = createSelector(
  selectFormValues,
  (formValues) => [
    { key: 'inboundIsaSender', label: 'ISA Sender', value: formValues.inboundIsaSender },
    { key: 'inboundGsSender', label: 'GS Sender', value: formValues.inboundGsSender },
  ],
);

export const selectStaticOutboundFields = createSelector(
  selectFormValues,
  (formValues) => [
    { key: 'outboundIsaReceiver', label: 'ISA Receiver', value: formValues.outboundIsaReceiver },
    { key: 'outboundGsReceiver', label: 'GS Receiver', value: formValues.outboundGsReceiver },
  ],
);

export const selectDynamicFieldValues = createSelector(
  selectFormValues,
  (formValues) => ({
    inbound: {
      isaSender: formValues.inboundIsaSender,
      gsSender: formValues.inboundGsSender,
      isaReceiver: formValues.inboundIsaReceiver,
      gsReceiver: formValues.inboundGsReceiver,
    },
    outbound: {
      isaReceiver: formValues.outboundIsaReceiver,
      gsReceiver: formValues.outboundGsReceiver,
      isaSender: formValues.outboundIsaSender,
      gsSender: formValues.outboundGsSender,
    },
    ediAttributes: {
      messageId: formValues.dynamicMessageId,
      network: formValues.dynamicNetwork,
      messageStatus: formValues.dynamicMessageStatus,
      controlNumber: formValues.dynamicControlNumber,
    },
  }),
);

// Legacy selector for backward compatibility
export const selectLastSearch = selectLastSearchParams;
