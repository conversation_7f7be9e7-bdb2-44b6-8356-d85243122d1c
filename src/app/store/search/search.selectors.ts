import { createFeatureSelector, createSelector } from '@ngrx/store';

import { SearchStateModel } from './search.reducer';

// Feature selector
export const selectSearchState = createFeatureSelector<SearchStateModel>('search');

// Basic selectors
export const selectFormValues = createSelector(
  selectSearchState,
  (state: SearchStateModel) => state.formValues,
);

export const selectLastSearchParams = createSelector(
  selectSearchState,
  (state: SearchStateModel) => state.lastSearchParams,
);

export const selectHasSearched = createSelector(
  selectSearchState,
  (state: SearchStateModel) => state.hasSearched,
);

export const selectIsLoading = createSelector(
  selectSearchState,
  (state: SearchStateModel) => state.isLoading,
);

// Derived selectors for better component integration
export const selectStaticInboundFields = createSelector(
  selectFormValues,
  (formValues) => [
    { key: 'isaSender', label: 'ISA Sender', value: formValues.isaSender },
    { key: 'gsSender', label: 'GS Sender', value: formValues.gsSender },
  ],
);

export const selectStaticOutboundFields = createSelector(
  selectFormValues,
  (formValues) => [
    { key: 'isaReceiver', label: 'ISA Receiver', value: formValues.isaReceiver },
    { key: 'gsReceiver', label: 'GS Receiver', value: formValues.gsReceiver },
  ],
);

export const selectDynamicFieldValues = createSelector(
  selectFormValues,
  (formValues) => ({
    inbound: {
      isaReceiver: formValues.dynamicIsaReceiver,
      gsReceiver: formValues.dynamicGsReceiver,
    },
    outbound: {
      isaSender: formValues.dynamicIsaSender,
      gsSender: formValues.dynamicGsSender,
    },
    ediAttributes: {
      messageId: formValues.dynamicMessageId,
      network: formValues.dynamicNetwork,
      messageStatus: formValues.dynamicMessageStatus,
      controlNumber: formValues.dynamicControlNumber,
    },
  }),
);

// Legacy selector for backward compatibility
export const selectLastSearch = selectLastSearchParams;
