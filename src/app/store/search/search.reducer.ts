import { createReducer, on } from '@ngrx/store';

import * as SearchActions from './search.actions';

// Simplified and type-safe search form values
export interface SearchFormValues {
  // Basic search options
  searchType: 'edi' | 'app';
  direction: 'inbound-outbound' | 'inbound' | 'outbound';
  transactionView: 'all' | 'group';
  equipmentSearchType: 'equipment';

  // Date range
  startDate: Date;
  endDate: Date;

  // Text inputs
  equipmentIds: string;
  transactionAttributes: string;

  // Static inbound/outbound fields
  isaSender: string;
  gsSender: string;
  isaReceiver: string;
  gsReceiver: string;

  // Dynamic additional filters
  dynamicIsaReceiver: string;
  dynamicGsReceiver: string;
  dynamicIsaSender: string;
  dynamicGsSender: string;
  dynamicMessageId: string;
  dynamicNetwork: string;
  dynamicMessageStatus: string;
  dynamicControlNumber: string;
}

// Search state model - simplified and focused on business data
export interface SearchStateModel {
  formValues: SearchFormValues;
  lastSearchParams: Record<string, string>;
  hasSearched: boolean;
  isLoading: boolean;
}

// Default state
export const initialState: SearchStateModel = {
  formValues: {
    // Basic search options
    searchType: 'edi',
    direction: 'inbound-outbound',
    transactionView: 'all',
    equipmentSearchType: 'equipment',

    // Date range - default to last 15 days
    startDate: new Date(new Date().setDate(new Date().getDate() - 15)),
    endDate: new Date(),

    // Text inputs
    equipmentIds: '',
    transactionAttributes: '',

    // Static inbound/outbound fields
    isaSender: '',
    gsSender: '',
    isaReceiver: '',
    gsReceiver: '',

    // Dynamic additional filters
    dynamicIsaReceiver: '',
    dynamicGsReceiver: '',
    dynamicIsaSender: '',
    dynamicGsSender: '',
    dynamicMessageId: '',
    dynamicNetwork: '',
    dynamicMessageStatus: '',
    dynamicControlNumber: '',
  },
  lastSearchParams: {},
  hasSearched: false,
  isLoading: false,
};

export const searchReducer = createReducer(
  initialState,
  on(SearchActions.updateFormValues, (state, { formValues }) => ({
    ...state,
    formValues: { ...state.formValues, ...formValues },
  })),
  on(SearchActions.saveSearchParams, (state, { searchParams }) => ({
    ...state,
    lastSearchParams: searchParams,
    hasSearched: true,
  })),
  on(SearchActions.setLoading, (state, { isLoading }) => ({
    ...state,
    isLoading,
  })),
  on(SearchActions.resetState, () => initialState),
  // Legacy action support for backward compatibility
  on(SearchActions.saveFormValues, (state, { payload }) => ({
    ...state,
    formValues: { ...state.formValues, ...payload },
  })),
);
