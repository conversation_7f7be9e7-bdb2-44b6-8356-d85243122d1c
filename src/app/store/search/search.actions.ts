import { createAction, props } from '@ngrx/store';

import { SearchFormValues } from './search.reducer';

// More specific and type-safe actions
export const updateFormValues = createAction(
  '[Search] Update Form Values',
  props<{ formValues: Partial<SearchFormValues> }>(),
);

export const saveSearchParams = createAction(
  '[Search] Save Search Parameters',
  props<{ searchParams: Record<string, string> }>(),
);

export const setLoading = createAction(
  '[Search] Set Loading',
  props<{ isLoading: boolean }>(),
);

export const resetState = createAction('[Search] Reset State');

// Legacy action for backward compatibility (can be removed after migration)
export const saveFormValues = createAction(
  '[Search] Save Form Values (Legacy)',
  props<{ payload: any }>(),
);
