import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { map, tap } from 'rxjs/operators';

import * as SearchActions from './search.actions';

@Injectable()
export class SearchEffects {
  constructor(private actions$: Actions) {}

  // Enhanced logging effect for all search actions
  logActions$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(
          SearchActions.updateFormValues,
          SearchActions.saveFormValues, // Legacy support
          SearchActions.saveSearchParams,
          SearchActions.setLoading,
          SearchActions.resetState,
        ),
        tap(action => {
          if (action.type.includes('Legacy')) {
            console.warn('Legacy action used:', action.type, '- Consider migrating to new actions');
          }
          console.log('Search action dispatched:', action);
        }),
        map(() => ({ type: '[Search] Action Logged' })),
      ),
    { dispatch: false },
  );

  // Future: Add effects for API calls, persistence, etc.
  // searchEffect$ = createEffect(() =>
  //   this.actions$.pipe(
  //     ofType(SearchActions.saveSearchParams),
  //     switchMap(({ searchParams }) =>
  //       this.searchService.search(searchParams).pipe(
  //         map(results => SearchActions.searchSuccess({ results })),
  //         catchError(error => of(SearchActions.searchFailure({ error })))
  //       )
  //     )
  //   )
  // );
}
